import Navbar from "../components/Navbar";
import Sidebar from "../components/Sidebar";
import DashboardCards from "../components/DashboardCards";
import RecentActivity from "../components/RecentActivity";
import QuickActions from "../components/QuickActions";
import Footer from "../components/Footer";

export default function Dashboard() {
  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Welcome Section */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
            <h1 className="text-3xl font-bold mb-2">Welcome back! 👋</h1>
            <p className="text-blue-100">Here's what's happening with your system today.</p>
          </div>

          {/* Stats Cards */}
          <DashboardCards />

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Recent Activity - Takes 2 columns on large screens */}
            <div className="lg:col-span-2">
              <RecentActivity />
            </div>

            {/* Quick Actions - Takes 1 column */}
            <div className="lg:col-span-1">
              <QuickActions />
            </div>
          </div>
        </main>
        <Footer />
      </div>
    </div>
  );
}